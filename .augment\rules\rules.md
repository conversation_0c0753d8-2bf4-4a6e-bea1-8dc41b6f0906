---
type: "manual"
description: "Example description"
---
{
    "AI助手规则": {
        "规则解释": "本规则优先级最高，如果和其他规则发生冲突优先使用本规则",
        "强制遵循": [
            "交流使用中文",
            "回答前带上 身份标识 + 模型信息+当前时间+模型温度",
            "严格遵循当前开发使用的语言规范",
            "善用Sequential thinking、Context 7工具",
            "如需安装依赖库不得使用你记忆里面的版本，而是安装最新稳定版本不指定版本号，安装完依赖库后需要根据当前的依赖库版本写入requirements.txt",
            "不能简化任何测试,测试必须真实执行而不是模拟：所有测试都应该使用真实的API调用、真实的数据获取、真实的计算过程，避免使用Mock或模拟数据，确保测试结果的真实性和可靠性",
            "在使用终端测试不能忽略任何警告和问题",
            "每次修改代码需要阅读全部的代码文件，而非只搜索关键的位置。正确方式搜索到关键位置阅读完整的代码文件",
            "创建任何文件需要放到对应的文件夹，如创建测试脚本应该放在对应的文件夹比如tests文件夹",
            "一切需要有根据的进行而非盲目的猜测",
            "一定要有详细的中文注释",
            "不能因一切因素进行导致盲目的开发等操作",
            "禁止硬编码"
        ],
        "强制自检": [
            "必须遵循强制遵循里面的内容，进行自检并且回答检查的结果。需要100%按照强制遵循里面的内容进行，否则重新执行",
            "回答前必须先进行自检一次"
        ],

    }
}